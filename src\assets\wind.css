@import url("./fonts/geist/geist.css");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=REM:ital,wght@0,100..900;1,100..900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: 76 63 63;
  --secondary: 107 42 0;
  --dark: 32 34 36;
  --text-clr: var(--dark);
  --base-clr: 103 103 105;
  --base-clr2: 247 248 251;
  --base-clr3: 30 30 30;
  --base-clr4: 255 255 255;
  --accent: 239 239 239;
  --orange: 255 137 0;

  --drawer-width: 18rem;
  --navbar-height: 3rem;
}

body {
  font-family: "Geist", ui-sans-serif, system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Ubuntu",
    "Segoe UI Symbol", "Noto Color Emoji";
  font-optical-sizing: auto;
  font-weight: 400;
  text-rendering: optimizeLegibility;
  font-synthesis-weight: none;
  font-variation-settings: "wdth" 100, "YTLC" 500;
  font-style: normal;
  @apply text-text-clr text-sm;
}

html,
body,
#app {
  height: 100%;
  overflow: hidden;
}

* {
  scrollbar-width: none;
  box-sizing: border-box;
}

@layer components {
  .input-style {
    @apply h-10 rounded-md border px-3;
  }
}

.bar {
  --bar-height: 100%;
  --bar-width: 2px;
  position: relative;
}

.bar::after {
  content: "";
  position: absolute;
  top: var(--bar-top);
  left: var(--bar-left);
  right: var(--bar-right);
  bottom: var(--bar-bottom);
  height: var(--bar-height);
  width: var(--bar-width);
  background-color: #0001;
}

.bar-right {
  --bar-right: 0;
  --bar-left: 115%;
  --bar-top: 0;
  --bar-bottom: 0;
}

.bar-left {
  --bar-right: 11%;
  --bar-left: 0;
  --bar-top: 0;
  --bar-bottom: 0;
}

.bar-top {
  --bar-right: 0;
  --bar-left: 0;
  --bar-top: 0;
  --bar-bottom: 115%;
  --bar-height: 2px;
  --bar-width: 100%;
}

.bar-bottom {
  --bar-right: 0;
  --bar-left: 0;
  --bar-top: 115%;
  --bar-bottom: 0;
  --bar-height: 2px;
  --bar-width: 100%;
}

@layer utilities {
  .sys-focus {
    box-shadow: 0 0 0 2px rgb(var(--primary));
  }
}

::-webkit-scrollbar {
  display: none;
}

.show-scrollbar {
  overflow: auto;
  padding: 0 0.15rem;
}

.show-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: theme("colors.text-clr");
  visibility: hidden;
}

.show-scrollbar:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}

.show-scrollbar::-webkit-scrollbar {
  width: 4px !important;
  height: 4px !important;
}

*:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgb(var(--primary));
}

button:focus {
  box-shadow: 0px 0px 0px 2px theme("colors.primary");
}

i,
svg {
  pointer-events: none;
}

.table_action {
  @apply hover:text-primary font-medium underline italic;
}

/* RIpple aEffect */

.__ripple {
  position: relative;
  overflow: hidden;
  isolation: isolate;
}
.__ripple .__ripple_child {
  z-index: -1;
  position: absolute;
  width: 0px;
  height: 0px;
  background-color: #ddd;
  filter: contrast(0.4);
  border-radius: 99999px;
  transform: translate3d(-50%, -50%, 0);
  pointer-events: none;
}

.__ripple_animation {
  top: var(--y);
  left: var(--x);
  animation: ripple 0.4s ease-out;
}

@keyframes ripple {
  100% {
    width: var(--btnWidth);
    height: var(--btnWidth);
    opacity: 0;
  }
}

.tooltip {
  position: relative;
}

.tooltip::after {
  content: "";
  position: absolute;
}

@layer base {
  .h1 {
    @apply font-semibold text-base px-2;
  }
  h1 {
    @apply font-semibold text-base px-2;
  }
}
