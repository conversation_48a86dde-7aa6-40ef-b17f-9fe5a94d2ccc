export default {
  quotations: `
		<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 64 64"><path fill="currentColor" d="M19.2 49.5c-1.2 0-2.3-.7-2.7-1.8l-3.3-8c-3.7-.6-7-2.5-9.1-5.4c-2.2-2.9-2.9-6.5-2-10c1.4-5.7 7.3-10 13.8-9.9c4.6.1 8.4 2 10.9 5.4c2.4 3.4 3 8 1.6 12.2c-1.3 3.8-3 7.6-4.6 11.3c-.6 1.5-1.3 3-1.9 4.4c-.4 1.1-1.5 1.8-2.7 1.8m-3.5-30.6c-4.4 0-8.4 2.8-9.3 6.5c-.5 2.2-.1 4.4 1.3 6.2c1.6 2.2 4.3 3.6 7.2 3.8l1.4.1l2.9 7c.1-.3.3-.7.4-1c1.6-3.6 3.2-7.3 4.4-11c1-2.8.6-5.9-1-8.1s-4.1-3.4-7.2-3.5zm36.6 30.7c-1.2 0-2.3-.7-2.7-1.8l-3.3-8c-3.7-.6-7-2.5-9.1-5.4c-2.2-2.9-2.9-6.5-2-10c1.4-5.7 7.3-10 13.8-9.9c4.6.1 8.4 2 10.8 5.4s3 8 1.6 12.2c-1.3 3.8-3 7.6-4.6 11.3c-.6 1.5-1.3 3-1.9 4.4c-.3 1.1-1.4 1.8-2.6 1.8m-3.5-30.7c-4.4 0-8.4 2.8-9.3 6.5c-.5 2.2-.1 4.4 1.3 6.3c1.6 2.2 4.3 3.6 7.2 3.8l1.4.1l2.9 7c.1-.3.3-.7.4-1c1.6-3.6 3.2-7.3 4.4-11c1-2.8.6-5.9-1-8.2c-1.6-2.2-4.1-3.4-7.2-3.5z"/></svg>
	`,
  sales: `<svg width="41" height="41" viewBox="0 0 41 41" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="20.2436" cy="20.1157" r="20.1157" fill="#55291B" fill-opacity="0.5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.201 11.064C12.0901 11.064 11.1895 11.9646 11.1895 13.0755V27.1565C11.1895 28.2675 12.0901 29.1681 13.201 29.1681H27.282C28.393 29.1681 29.2936 28.2675 29.2936 27.1565V13.0755C29.2936 11.9646 28.393 11.064 27.282 11.064H13.201ZM16.2184 21.1218C16.2184 20.5663 15.7681 20.116 15.2126 20.116C14.6571 20.116 14.2068 20.5663 14.2068 21.1218V25.1449C14.2068 25.7004 14.6571 26.1507 15.2126 26.1507C15.7681 26.1507 16.2184 25.7004 16.2184 25.1449V21.1218ZM20.2415 17.0987C20.797 17.0987 21.2473 17.549 21.2473 18.1045V25.1449C21.2473 25.7004 20.797 26.1507 20.2415 26.1507C19.686 26.1507 19.2357 25.7004 19.2357 25.1449V18.1045C19.2357 17.549 19.686 17.0987 20.2415 17.0987ZM26.2762 15.0871C26.2762 14.5316 25.8259 14.0813 25.2704 14.0813C24.715 14.0813 24.2646 14.5316 24.2646 15.0871V25.1449C24.2646 25.7004 24.715 26.1507 25.2704 26.1507C25.8259 26.1507 26.2762 25.7004 26.2762 25.1449V15.0871Z" fill="white"/>
</svg>
`,
insuranceApp:`<svg width="432" height="512" viewBox="0 0 432 512" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M338.696 312.746L345.532 319.041L343.926 326.558L334.821 324.948L332.134 318.166L338.696 312.746Z" fill="currentColor"/>
<path d="M212.768 212.19L213.839 220.244L210.436 225.494L204.913 224.68L204.201 215.412L211.699 211.653L212.768 212.19Z" fill="#EACDB0"/>
<path d="M156.473 213.801L192.416 257.293L199.914 223.466L213.839 226.151L205.806 277.16L192.299 286.567L156.533 262.663L156.473 213.801Z" fill="#A1BDCC"/>
<path d="M186.119 313.197L187.984 392.993L174.201 496.07L162.667 496.207L154.258 401.348L137.35 306.59L186.119 313.197Z" fill="#8C9498"/>
<path d="M162.717 494.83L162.791 501.083L175.338 507.187L181.501 500.86L175.189 494.682L162.717 494.83Z" fill="#3A3029"/>
<path d="M123.465 497.843L133.384 506.295L130.904 511.266L112.556 507.289V497.843H123.465Z" fill="#3A3029"/>
<path d="M146.12 344.587L137.877 421.932L126.999 499.027L111.485 498.287L112.051 418.309L102.711 344.13L124.645 333.888L146.12 344.587Z" fill="#8C9498"/>
<path d="M114.506 207.985L144.642 202.312C144.642 202.312 165.151 215.54 180.702 255.575C196.539 296.341 194.3 358.781 194.3 358.781C194.3 358.781 111.377 360.111 97.7971 360.432L105.705 274.068L114.506 207.985Z" fill="#A1BDCC"/>
<path d="M134.997 208.332L124.218 207.947C124.002 205.079 124.015 202.199 124.257 199.334C121.215 198.474 117.677 197.117 116.961 194.032C116.791 193.046 116.787 192.039 116.949 191.052L118.279 178.745C118.434 177.326 118.599 175.865 119.284 174.614C120.487 172.419 123.022 171.332 125.451 170.746C131.65 169.258 138.568 170.186 143.686 173.995C145.096 174.957 146.229 176.273 146.972 177.812C148.007 180.22 147.521 182.98 147.018 185.554L145.624 192.68C145.09 195.413 144.267 198.532 141.747 199.703C140.41 200.325 138.781 200.27 137.589 201.142C135.488 202.677 135.991 205.922 134.997 208.332Z" fill="#EACDB0"/>
<path d="M155.024 163.834C155.031 161.157 154.799 158.484 154.328 155.849C153.601 151.62 149.831 150.188 146.576 150.327C139.547 150.626 132.739 147.228 125.902 151.506C121.993 153.952 116.731 155.131 113.201 158.308C111.665 159.811 110.456 161.618 109.652 163.613C106.509 170.76 106.563 180.006 109.785 186.292C110.5 187.652 111.334 188.945 112.277 190.157C114.149 192.627 116.388 195.687 118.942 196.346C118.979 193.244 116.828 183.777 118.558 180.998C121.302 176.589 127.589 177.122 131.04 177.635C134.805 178.194 138.181 181.343 141.911 181.599C144.594 181.782 148 179.523 150.264 177.325C153.81 173.88 154.968 168.835 155.024 163.834Z" fill="#3A3029"/>
<path d="M136.324 208.01L165.723 275.298L177.104 358.829L134.996 359.715C134.996 359.715 139.835 297.835 138.096 289.756C136.357 281.678 132.904 264.9 129.166 246.025C128.305 241.679 122.016 208.355 121.973 208.554L136.324 208.01Z" fill="white"/>
<path d="M269.302 216.287L264.579 215.066L255.555 220.244L229.847 282.529L237.881 292.194L259.84 263.736L269.302 216.287Z" fill="#8AAD9F"/>
<path d="M308.75 494.659L309.25 499.61L300.292 505.785L295.064 501.444L299.293 495.882L308.252 489.708L308.75 494.659Z" fill="#514943"/>
<path d="M308.527 343.276L310.044 358.313L296.61 409.978L309.085 493.001L299.902 496.177L275.689 415.123L271.847 391.544L271.987 341.626L308.527 343.276Z" fill="#CAB5A1"/>
<path d="M237.904 360.036L257.218 499.15H265.997L267.577 409.978L283.556 347.389L237.904 360.036Z" fill="#CAB5A1"/>
<path d="M265.947 498.051V503.047L256.454 508.045L251.707 503.047L256.454 498.051H265.947Z" fill="#514943"/>
<path d="M310.538 300.587C305.544 271.553 297.811 243.373 295.015 213.803C287.628 213.803 277.428 210.365 269.738 212.41C269.738 212.41 269.607 212.44 269.369 212.521C266.457 213.314 263.938 215.154 262.295 217.691C262.275 217.722 262.256 217.755 262.236 217.787C259.499 220.792 256.355 225.433 253.414 232.595C245.916 250.851 215.923 358.239 215.388 386.159L215.667 386.225C222.333 388.245 229.141 389.762 236.034 390.764L236.05 390.767H236.056C257.031 393.968 280.501 393.937 299.591 393.614C302.111 393.571 328.825 394.445 328.449 392.714C321.814 362.14 315.844 331.432 310.538 300.587Z" fill="#8AADA2"/>
<path d="M293.849 215.143L301.436 219.826L319.824 270.179L340.176 311.524L331.071 319.041L304.293 281.454L288.493 230.714L293.849 215.143Z" fill="#8AAD9F"/>
<path d="M257.019 167.791C257.019 167.791 251.33 172.457 254.008 189.639C256.686 206.821 255.079 218.097 255.079 218.097C255.079 218.097 260.97 220.782 265.255 218.097C269.539 215.412 271.146 214.875 269.539 207.895C267.932 200.915 264.719 167.625 264.719 167.625L257.019 167.791Z" fill="#9E6652"/>
<path d="M271.134 173.596C267.883 174.55 264.982 176.439 262.789 179.027C260.596 181.616 259.207 184.792 258.794 188.163C257.007 186.491 255.647 184.414 254.826 182.106C254.005 179.798 253.749 177.327 254.077 174.899C254.406 172.471 255.311 170.157 256.715 168.152C258.12 166.147 259.984 164.509 262.15 163.375C266.223 165.572 269.275 169.283 270.65 173.711L271.134 173.596Z" fill="#9E6652"/>
<path d="M257.901 182.349C261.262 164.528 283.436 167.607 289.797 182.04C293.414 190.247 292.569 206.285 282.389 208.97C282.235 210.547 282.029 213.505 280.829 214.559C279.528 215.702 275.431 214.611 273.825 214.24C273.369 214.173 272.942 213.98 272.59 213.683C272.186 213.276 272.118 212.188 272.111 210.981C270.88 210.894 269.666 210.654 268.495 210.265C263.338 208.563 259.603 203.77 258.058 198.544C256.513 193.319 256.891 187.704 257.901 182.349Z" fill="#EACDB0"/>
<path d="M262.577 163.328C262.577 163.328 260.435 172.993 272.753 175.678C285.07 178.363 293.072 188.028 290.946 198.23C288.819 208.432 279.715 215.949 284.536 218.634C289.356 221.318 307.03 218.634 307.03 218.634C307.03 218.634 317.741 182.658 303.28 167.087C288.819 151.515 267.933 150.979 262.577 163.328Z" fill="#9E6652"/>
<path d="M220.829 10.7165V5.45625L218.412 0.498167C218.252 0.336597 218.062 0.209273 217.852 0.12396C217.642 0.0386477 217.416 -0.0028617 217.19 0.00196425C216.963 0.0067902 216.74 0.0578504 216.534 0.15203C216.328 0.246209 216.143 0.381531 215.99 0.54974L213.967 5.45625V10.7165C212.064 10.7165 208.959 12.2121 208.959 14.0599C208.975 14.9619 209.347 15.8207 209.994 16.4477C210.641 17.0747 211.51 17.4186 212.409 17.4038H213.967V195.719C213.967 205.779 208.661 214.148 201.042 213.969C195.136 213.829 189.986 208.796 189.118 200.581C189.018 199.689 188.591 198.866 187.92 198.272C187.25 197.677 186.384 197.353 185.488 197.361H185.352C184.846 197.35 184.344 197.447 183.878 197.646C183.413 197.845 182.995 198.141 182.652 198.514C182.31 198.888 182.05 199.33 181.892 199.812C181.733 200.293 181.678 200.803 181.731 201.308C183.238 212.992 192.488 221.352 201.267 221.352C208.129 221.352 213.755 218.164 217.032 212.418C221.089 205.304 220.829 197.483 220.828 189.675V17.4038H221.897C222.796 17.4186 223.665 17.0747 224.312 16.4477C224.959 15.8207 225.331 14.9619 225.347 14.0599C225.348 12.2137 222.735 10.7165 220.829 10.7165Z" fill="#59595B"/>
<g opacity="0.26">
<path d="M220.83 110.676L213.897 103.451V91.5167H220.83V110.676Z" fill="black"/>
</g>
<path d="M166.433 126.316L167.667 118.683C171.951 92.2448 182.086 67.1039 197.328 45.1054L216.591 17.3053C187.142 18.5577 159.083 30.2047 137.37 50.1884C115.658 70.172 101.689 97.2064 97.9319 126.515C108.079 123.472 119.832 121.728 132.407 121.728C143.906 121.67 155.358 123.214 166.433 126.316Z" fill="#4C3F3F"/>
<path d="M267.173 110.968L270.474 127.354H271.708C282.437 123.795 295.15 121.728 308.838 121.728C322.48 121.728 335.269 123.781 346.126 127.317C342.415 96.9274 327.74 68.9497 304.869 48.6609C281.998 28.3722 252.512 17.1749 221.973 17.181C220.633 17.181 219.298 17.2094 217.969 17.2512L233.357 39.807C252.219 63.0678 260.88 86.6308 267.173 110.968Z" fill="#4C3F3F"/>
<path d="M167.669 118.683L166.435 126.315L166.767 126.412C179.751 120.625 197.546 117.305 217.2 117.305C237.896 117.305 256.572 120.985 269.739 127.353H270.476L267.175 110.967C260.883 86.6303 252.221 63.0673 233.359 39.8065L217.969 17.2538C217.509 17.2686 217.049 17.2858 216.591 17.3054L197.33 45.1065C182.088 67.1046 171.954 92.2449 167.669 118.683Z" fill="#4C3F3F"/>
<path d="M166.433 126.316L166.39 126.579C166.514 126.523 166.64 126.468 166.765 126.413L166.433 126.316Z" fill="white"/>
<path d="M200.985 223.466L197.579 217.412L197.772 207.895L200.985 208.969L202.056 213.801L207.948 219.17L206.211 224.68L200.985 223.466Z" fill="#EACDB0"/>
<path d="M201.089 215.662L201.861 209.425L208.2 204.701L212.541 208.073L211.529 213.263L201.089 215.662Z" fill="#EACDB0"/>
<path d="M114.698 207.894L102.463 217.892L71.8514 273.402L87.9189 293.805L98.0949 290.583L122.196 236.888L114.698 207.894Z" fill="#A1BDCC"/>
<path d="M117.911 281.992L121.66 290.546V322.319L115.899 322.8L107.736 295.36L117.911 281.992Z" fill="#EACDB0"/>
<path d="M130.229 277.16L136.121 282.53L143.619 313.672L138.799 316.357L126.48 291.658L117.911 284.678L119.517 277.161L130.229 277.16Z" fill="#EACDB0"/>
<path d="M83.029 230.277C83.029 230.277 78.7419 231.644 76.8654 235.407C74.9888 239.17 69.9836 255.477 72.4862 266.767C74.9888 278.056 76.2414 292.482 100.639 297.5C112.525 298.754 110.649 288.719 119.407 286.21C128.165 283.701 136.505 285.165 132.338 271.575C128.172 257.986 118.783 256.732 110.651 242.306C102.518 227.88 93.5723 223.267 83.029 230.277Z" fill="#C2969C"/>
<path d="M80.012 231.857L61.6755 242.796L47.215 248.165L48.8215 254.608L63.8174 253.535L78.8134 247.091L80.012 231.857Z" fill="#C2969C"/>
<path d="M98.0949 226.151L113.627 228.299L124.808 221.417L127.552 225.079L117.911 235.814L106.664 239.035L98.0949 226.151Z" fill="#C2969C"/>
<path d="M88.4894 199.083C88.4894 199.083 98.7879 203.114 99.8871 212.298C99.8871 212.298 104.79 206.272 100.842 200.189C96.8943 194.106 94.6448 191.858 88.6859 194.599C82.7271 197.339 88.4894 199.083 88.4894 199.083Z" fill="#754C3D"/>
<path d="M98.724 207.299C94.7677 194.114 78.2743 198.213 74.622 209.615C72.5443 216.101 74.459 228.141 82.3582 229.353C82.6015 230.532 82.9909 232.748 83.9817 233.448C85.0542 234.207 88.0614 233.057 89.244 232.647C89.5821 232.56 89.8893 232.38 90.1309 232.127C90.4035 231.789 90.3685 230.961 90.278 230.049C91.2007 229.886 92.0993 229.607 92.9529 229.219C96.7112 227.523 99.1495 223.605 99.8974 219.536C100.645 215.467 99.9134 211.261 98.724 207.299Z" fill="#EACDB0"/>
<path d="M91.3526 202.165C87.744 208.056 80.4312 209.953 77.0135 215.653C77.0135 215.653 74.7795 219.382 77.101 226.629C74.7257 227.129 72.2564 226.92 69.9983 226.028C67.7402 225.136 65.7922 223.601 64.3951 221.611C59.3539 214.608 63.9172 197.701 63.9172 197.701C63.9172 197.701 71.6508 189.066 84.262 189.321C88.5409 189.408 92.867 191.7 92.9699 196.541C92.9901 198.534 92.4278 200.489 91.3526 202.165Z" fill="#754C3D"/>
<path d="M71.3298 489.201C71.3298 489.201 71.942 494.255 78.1143 494.339C84.2867 494.422 86.8022 498.584 81.1442 498.506C75.4862 498.428 68.7501 499.51 66.5093 495.282C64.2686 491.054 69.6626 486.197 71.3298 489.201Z" fill="#F7F7F9"/>
<path d="M52.3616 466.619C52.3616 466.619 50.1584 467.093 49.0735 471.204C47.9886 475.315 22.3619 468.258 13.5332 474.326C4.70439 480.393 3.55777 485.531 2.45077 494.806C0.108677 514.403 35.1367 512.275 47.0308 507.796C58.9249 503.318 79.0125 501.531 73.6436 480.309C72.621 479.78 52.3616 466.619 52.3616 466.619Z" fill="#F7F7F9"/>
<path d="M75.3515 478.333L73.5855 479.726C73.5855 479.726 64.818 482.565 54.8719 471.305C50.9578 465.333 52.2109 464.33 52.2109 464.33L54.4393 464.828C54.4393 464.828 58.553 477.834 75.3515 478.333Z" fill="#4B3E3E"/>
<path d="M67.0057 447.572C67.0057 447.572 67.8802 446.231 70.3684 446.18C72.8566 446.128 76.6329 450.209 77.5475 450.541C78.4621 450.873 80.6715 451.418 78.4497 452.256C76.228 453.094 68.0891 450.1 67.0057 447.572Z" fill="#F7E8E7"/>
<path d="M58.4295 453.016C58.4295 453.016 62.3791 447.588 68.2032 447.549C71.6441 447.525 74.7326 448.974 77.3149 451.18C78.6303 452.303 79.6678 453.659 80.9024 454.851C81.7214 455.642 82.743 456.003 83.5408 456.866C84.507 458.013 85.1954 459.369 85.5524 460.827C85.9093 462.285 85.9251 463.806 85.5984 465.272C84.8675 467.959 87.9092 475.844 78.9358 477.773C69.9625 479.702 58.0766 474.272 55.5447 469.603C53.0128 464.934 50.7427 460.201 58.4295 453.016Z" fill="#F7F7F9"/>
<path d="M59.8884 453.678C59.8884 453.678 57.2233 452.059 54.9938 453.163C52.7644 454.266 50.9989 453.867 51.054 456.36C51.109 458.853 50.6893 465.084 53.6584 466.999C56.6276 468.915 55.9295 463.794 57.4424 462.03C58.9553 460.266 61.5103 454.44 59.8884 453.678Z" fill="#F7E8E7"/>
<path d="M71.4096 490.646C71.4096 490.646 71.8947 497.866 75.4883 498.429C79.0819 498.992 87.7764 502.724 86.2049 504.765C84.6334 506.806 82.0897 504.709 79.5176 504.673C76.9456 504.638 69.4492 505.711 63.1924 503.232C57.3081 502.491 54.6337 505.12 54.6337 505.12L71.4096 490.646Z" fill="#F7F7F9"/>
<path d="M6.601 485.162C5.71828 484.9 1.01197 483.601 0.822668 478.057C0.633367 472.514 4.2692 468.008 9.8073 468.11C15.3454 468.212 15.2435 473.239 14.0949 473.98C12.9462 474.721 6.17559 475.537 7.16068 481.637C7.92921 484.242 6.601 485.162 6.601 485.162Z" fill="#F7F7F9"/>
<path d="M79.8855 232.057C79.8855 232.057 80.4209 239.037 84.1699 239.037C87.9189 239.037 95.865 235.753 97.0239 234.205C98.6304 232.057 95.597 227.489 95.597 227.489L98.0949 226.151C98.0949 226.151 115.769 248.702 106.664 254.608C97.56 260.515 85.2409 264.273 81.4919 256.756C77.7429 249.239 77.4981 233.704 77.4981 233.704L79.8855 232.057Z" fill="#F2D263"/>
<path d="M122.196 321.726L127.016 325.485L125.409 327.633L116.84 326.022L115.769 322.263L122.196 321.726Z" fill="#3A3029"/>
<path d="M138.263 315.283L143.083 311.792L147.368 311.524L148.439 314.745L139.334 317.967L138.263 315.283Z" fill="#3A3029"/>
<path d="M71.8895 273.148L90.1905 270.892L133.65 279.434L133.529 289.534L88.1365 294.038L74.0665 289.413L71.8895 273.148Z" fill="#AECCDC"/>
<path d="M133.64 280.242L140.204 282.161L143.556 291.015L139.335 295.953L137.728 288.972L133.542 288.494L133.64 280.242Z" fill="currentColor"/>
<path d="M47.3883 248.861L39.7169 248.703V250.314L45.6085 253.536L44.5375 254.609L45.6085 255.146L48.5858 253.662L47.3883 248.861Z" fill="#EACDB0"/>
<path d="M125.097 221.904L126.481 219.171L130.23 218.634L131.836 221.319L130.765 224.541L127.496 224.88L125.097 221.904Z" fill="#EACDB0"/>
<path d="M380.345 348.036L359.457 343.741L348.21 324.948L342.854 329.243L350.888 354.479L368.562 365.218L380.345 348.036Z" fill="#B26650"/>
<path d="M362.135 436.095V493.01H370.705L376.06 458.646L380.88 432.336L362.135 436.095Z" fill="#2E4661"/>
<path d="M380.881 430.187L384.094 454.886L388.379 494.62L396.948 494.083V463.479L401.233 432.336L380.881 430.187Z" fill="#2E4661"/>
<path d="M376.864 347.231C376.864 347.231 367.759 345.62 364.01 359.581C360.261 373.541 350.085 414.349 349.549 435.826C349.014 457.302 362.939 443.343 379.542 446.028C396.145 448.713 409.802 448.981 409.802 439.316C409.802 429.652 415.693 347.499 376.864 347.231Z" fill="#B26650"/>
<path d="M371.637 319.483C379.221 306.074 396.211 314.654 397.375 328.011C398.037 335.607 392.966 348.273 384.043 347.63C383.486 348.857 382.512 351.179 381.257 351.697C379.898 352.258 376.9 350.253 375.711 349.512C375.364 349.333 375.073 349.06 374.871 348.725C374.658 348.287 374.901 347.392 375.226 346.42C374.26 346.012 373.349 345.484 372.515 344.849C368.832 342.064 367.141 337.178 367.329 332.55C367.518 327.923 369.359 323.512 371.637 319.483Z" fill="#EACDB0"/>
<path d="M382.221 315.999C382.221 315.999 394.356 319.041 394.356 324.109C394.356 331.729 394.861 336.842 394.356 341.373L402.951 336.274L408.007 322.081C408.007 322.081 406.996 310.423 400.929 308.395C394.862 306.368 382.221 304.34 382.221 304.34L372.615 307.383L365.537 306.876L367.984 328.165C367.984 328.165 371.098 312.957 382.221 315.999Z" fill="#754C3D"/>
<path d="M386.236 349.11L401.232 354.48L414.622 387.77L425.333 411.932L419.442 415.691L402.303 391.529L386.236 349.11Z" fill="#B26650"/>
<path d="M419.442 415.691L423.191 421.058L425.334 418.911H431.761L425.334 411.93L419.442 415.691Z" fill="#EACDB0"/>
<path d="M362.135 493.01L357.315 497.842L359.458 501.064L369.098 497.842V493.01H362.135Z" fill="#8C9498"/>
<path d="M389.807 494.531L391.592 501.601L397.483 503.749L400.697 502.139L395.707 494.162L389.807 494.531Z" fill="#8C9498"/>
<path d="M348.21 324.948L342.854 321.189L336.427 322.8L337.498 326.558L343.475 328.745L348.21 324.948Z" fill="#EACDB0"/>
<path d="M342.319 328.323L344.462 324.008L340.177 321.86L334.128 323.201L335.357 326.024L342.319 328.323Z" fill="currentColor"/>
</svg>
`,
  chevron_down: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M23 8v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-2v-1h-1v-1H9v-1H8v-1H7v-1H6v-1H5v-1H4v-1H3v-1H2V9H1V8h1V7h1V6h1v1h1v1h1v1h1v1h1v1h1v1h1v1h1v1h2v-1h1v-1h1v-1h1v-1h1V9h1V8h1V7h1V6h1v1h1v1z"/></svg>`,
  menu: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg>`,

  close: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>`,

  filter: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.97 20.344L9 21v-8.5L4.52 7.572A2 2 0 0 1 4 6.227V4h16v2.172a2 2 0 0 1-.586 1.414L15 12v1.5m3.42 2.11a2.1 2.1 0 0 1 2.97 2.97L18 22h-3v-3z"/></svg>
	`,
  delete: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="1.5"><path stroke-linecap="round" d="M20.5 6h-17m15.333 2.5l-.46 6.9c-.177 2.654-.265 3.981-1.13 4.79s-2.196.81-4.856.81h-.774c-2.66 0-3.991 0-4.856-.81c-.865-.809-.954-2.136-1.13-4.79l-.46-6.9"/><path d="M6.5 6h.11a2 2 0 0 0 1.83-1.32l.034-.103l.097-.291c.083-.249.125-.373.18-.479a1.5 1.5 0 0 1 1.094-.788C9.962 3 10.093 3 10.355 3h3.29c.262 0 .393 0 .51.019a1.5 1.5 0 0 1 1.094.788c.055.106.097.23.18.479l.097.291A2 2 0 0 0 17.5 6"/></g></svg>
	`,
  downAngle: `
		<svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M.5 3.85L6.65 10a.48.48 0 0 0 .7 0l6.15-6.15"/></svg>
	`,
  group: `
		<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="5" cy="3.75" r="2.25"/><path d="M9.5 13.5h-9v-1a4.5 4.5 0 0 1 9 0ZM9 1.5A2.25 2.25 0 0 1 9 6m1.6 2.19a4.5 4.5 0 0 1 2.9 4.2v1.11H12"/></g></svg>
	`,
  edit: `
		<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M6.13 12.06C4.6 13.6 2 14.11.5 12.57C2.5 10.5.5 9.5 2 8a2.9 2.9 0 1 1 4.09 4.1Z"/><path d="M12.92 1.08A2 2 0 0 0 11.44.5a2 2 0 0 0-1.44.67l-5.38 6A2.85 2.85 0 0 1 6.13 8a3 3 0 0 1 .77 1.31L12.83 4a2 2 0 0 0 .67-1.43a2 2 0 0 0-.58-1.49Z"/></g></svg>
	`,
  import: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="m1.91 9.5l-1.3 2.55a1 1 0 0 0 0 1a1 1 0 0 0 .87.47h11a1 1 0 0 0 .87-.47a1 1 0 0 0 0-1L12.09 9.5ZM5 2.5l2-2l2 2m-2-2v6"/><path d="M3 4.5a1 1 0 0 0-1 1v4h10v-4a1 1 0 0 0-1-1"/></g></svg>
	`,
  provider: `
		<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24"><path fill="currentColor" fill-rule="evenodd" d="M10.75 2h2c1.886 0 2.828 0 3.414.586S16.75 4.114 16.75 6v15.25h5a.75.75 0 0 1 0 1.5h-20a.75.75 0 0 1 0-1.5h5V6c0-1.886 0-2.828.586-3.414S8.864 2 10.75 2m1 2.25a.75.75 0 0 1 .75.75v1.25h1.25a.75.75 0 0 1 0 1.5H12.5V9A.75.75 0 0 1 11 9V7.75H9.75a.75.75 0 0 1 0-1.5H11V5a.75.75 0 0 1 .75-.75M9 12a.75.75 0 0 1 .75-.75h4a.75.75 0 0 1 0 1.5h-4A.75.75 0 0 1 9 12m0 3a.75.75 0 0 1 .75-.75h4a.75.75 0 0 1 0 1.5h-4A.75.75 0 0 1 9 15m2.75 3.25a.75.75 0 0 1 .75.75v2.25H11V19a.75.75 0 0 1 .75-.75" clip-rule="evenodd"/><path fill="currentColor" d="M20.913 5.889c.337.504.337 1.206.337 2.611v12.75h.5a.75.75 0 0 1 0 1.5h-20a.75.75 0 1 1 0-1.5h.5V8.5c0-1.405 0-2.107.337-2.611a2 2 0 0 1 .552-.552c.441-.295 2.537-.332 3.618-.336q-.005.437-.004.91V7.25H4.25a.75.75 0 1 0 0 1.5h2.503v1.5H4.25a.75.75 0 0 0 0 1.5h2.503v1.5H4.25a.75.75 0 0 0 0 1.5h2.503v6.5h10v-6.5h2.497a.75.75 0 1 0 0-1.5h-2.497v-1.5h2.497a.75.75 0 1 0 0-1.5h-2.497v-1.5h2.497a.75.75 0 0 0 0-1.5h-2.497V5.91q.001-.471-.004-.91c1.081.005 3.17.042 3.612.337a2 2 0 0 1 .552.552" opacity=".5"/></svg>
	`,
  coverage: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M8.315 1.031a.5.5 0 0 0-.5.5v1.407H6.409a.5.5 0 0 0-.5.5v1.625a.5.5 0 0 0 .5.5h1.406v1.406a.5.5 0 0 0 .5.5H9.94a.5.5 0 0 0 .5-.5V5.563h1.406a.5.5 0 0 0 .5-.5V3.438a.5.5 0 0 0-.5-.5H10.44V1.53a.5.5 0 0 0-.5-.5zm-7.732 9.75l2.444 2.037a2 2 0 0 0 1.28.463h6.443c.46 0 .833-.373.833-.833c0-.92-.746-1.667-1.667-1.667H5.437"/><path d="m3.583 9.781l.75.75a1.06 1.06 0 1 0 1.5-1.5L4.669 7.867a2 2 0 0 0-1.414-.586H.583"/></g></svg>
	`,
  insured: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="5" cy="2.75" r="2.25"/><path d="M6 6.61A4.49 4.49 0 0 0 .5 11v1.5H6m4.67.97h0a.5.5 0 0 1-.34 0h0A4.48 4.48 0 0 1 7.5 9.31V8a.47.47 0 0 1 .5-.5h5a.47.47 0 0 1 .5.5v1.31a4.48 4.48 0 0 1-2.83 4.16Z"/></g></svg>
	`,
  plus: `
		<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 14 14"><path fill="currentColor" fill-rule="evenodd" d="M8 1a1 1 0 0 0-2 0v5H1a1 1 0 0 0 0 2h5v5a1 1 0 1 0 2 0V8h5a1 1 0 1 0 0-2H8z" clip-rule="evenodd"/></svg>
	`,
  slash: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="m7 21l7.9-18H17L9.1 21z"/></svg>
	`,
  back: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M13.5 7H.5M4 3.5L.5 7L4 10.5"/></svg>
	`,
  forward: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M.5 7h13M10 10.5L13.5 7L10 3.5"/></svg>
	`,
  spinner: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M10.72,19.9a8,8,0,0,1-6.5-9.79A7.77,7.77,0,0,1,10.4,4.16a8,8,0,0,1,9.49,6.52A1.54,1.54,0,0,0,21.38,12h.13a1.37,1.37,0,0,0,1.38-1.54,11,11,0,1,0-12.7,12.39A1.54,1.54,0,0,0,12,21.34h0A1.47,1.47,0,0,0,10.72,19.9Z"><animateTransform attributeName="transform" dur="0.75s" repeatCount="indefinite" type="rotate" values="0 12 12;360 12 12"/></path></svg>
	`,
  close: `
		<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 14 14"><path fill="currentColor" fill-rule="evenodd" d="M1.707.293A1 1 0 0 0 .293 1.707L5.586 7L.293 12.293a1 1 0 1 0 1.414 1.414L7 8.414l5.293 5.293a1 1 0 0 0 1.414-1.414L8.414 7l5.293-5.293A1 1 0 0 0 12.293.293L7 5.586z" clip-rule="evenodd"/></svg>
	`,
  eye: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><path d="M13.23 6.246c.166.207.258.476.258.754c0 .279-.092.547-.258.754C12.18 9.025 9.79 11.5 7 11.5S1.82 9.025.77 7.754A1.2 1.2 0 0 1 .512 7c0-.278.092-.547.258-.754C1.82 4.975 4.21 2.5 7 2.5s5.18 2.475 6.23 3.746"/><path d="M7 9a2 2 0 1 0 0-4a2 2 0 0 0 0 4"/></g></svg>
	`,
  eyeSlash: `
		<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M1.68 4.206C2.652 6.015 4.67 7.258 7 7.258s4.348-1.243 5.322-3.052M2.75 5.596L.5 7.481m4.916-.415L4.333 9.794m6.917-4.198l2.25 1.885m-4.92-.415l1.083 2.728"/></svg>
	`,
  search: `
		<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 14 14"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" d="M6 11.5a5.5 5.5 0 1 0 0-11a5.5 5.5 0 0 0 0 11m7.5 2L10 10"/></svg>
	`,
  no_data: `
		<svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" width="862.70323" height="644.78592" viewBox="0 0 862.70323 644.78592" xmlns:xlink="http://www.w3.org/1999/xlink"><polygon points="629.943 612.644 612.777 612.644 604.608 546.435 629.943 546.435 629.943 612.644" fill="#9e616a"/><path d="M807.65107,769.99215H795.34112l-2.19727-11.62205-5.62754,11.62205H754.86738A7.33919,7.33919,0,0,1,750.697,756.6135l26.07247-18.00658v-11.7495l27.42368,1.63683Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><polygon points="731.923 590.981 718.148 601.224 672.085 552.969 692.415 537.851 731.923 590.981" fill="#9e616a"/><path d="M925.58816,737.04791,915.71,744.39344l-8.69827-8.015,2.41922,12.68419-26.19923,19.48211a7.33918,7.33918,0,0,1-11.32976-8.24721l10.17712-30.00728-7.0111-9.42842,22.98294-15.05066Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M818.57583,398.64705s32.56879,28.13791,17.542,108.35207l-18.3454,78.59653,59.8294,99.2561-19.07664,23.20771-77.77961-107.4334-28.18529-66.11365L744.6516,416.843Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><polygon points="599.447 425.746 597.488 456.084 603.483 585.365 631.692 580.452 637.083 488.406 599.447 425.746" fill="#2f2e41"/><polygon points="237.445 628.211 252.796 628.21 260.098 569.001 237.443 569.002 237.445 628.211" fill="#ffb6b6"/><path d="M402.178,750.80612l4.32074-.00018,16.86888-6.86018,9.0412,6.85913H432.41A19.26648,19.26648,0,0,1,451.67546,770.07v.62605l-49.49658.00183Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><polygon points="296.932 618.538 311.905 621.918 332.071 565.772 309.972 560.782 296.932 618.538" fill="#ffb6b6"/><path d="M462.86463,740.39329l4.21465.9516,17.96568-2.97583,7.3082,8.68223.0012.00027a19.26648,19.26648,0,0,1,14.54854,23.03569l-.1379.61067L458.48379,759.7967Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M386.6516,393.843c-7.19708,21.70636-6.43618,45.268,1.72992,70.55606l3.49087,142.37821S386.67128,700.146,403.4543,733.00177h24.34l12.05112-134.75129,1.5133-90.44591,52.18244,76.30583L460.30462,730.79868l29.9568,2.678,53.93408-159.1909L477.6516,419.843Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M667.346,332.01487c18.61732-16.77656,46.30893-25.21208,69.53714-15.805a115.466,115.466,0,0,0-51.888,59.93484c-3.6979,9.83846-6.78644,21.16623-15.88188,26.43349-5.65933,3.27753-12.70027,3.4377-19.04568,1.85557-6.34568-1.58237-12.16226-4.75415-17.89913-7.89422l-1.63218-.03691C637.86406,372.53682,648.72872,348.79142,667.346,332.01487Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M736.75328,316.71942A98.69239,98.69239,0,0,0,681.847,342.64994a42.50049,42.50049,0,0,0-8.34534,10.37667,24.37584,24.37584,0,0,0-2.81751,12.51568c.10054,4.05833.67335,8.19792-.21438,12.21a14.92537,14.92537,0,0,1-7.42454,9.68865c-4.54586,2.613-9.7595,3.43673-14.886,4.0651-5.692.69769-11.61526,1.33219-16.54238,4.5248-.597.38683-1.16231-.56211-.56622-.94836,8.57235-5.5546,19.41969-3.5335,28.63724-7.24065,4.30108-1.72983,8.10691-4.76631,9.454-9.35719,1.17794-4.01452.5909-8.2838.45359-12.39207a26.01068,26.01068,0,0,1,2.299-12.34028,39.29038,39.29038,0,0,1,7.9156-10.65924,95.74917,95.74917,0,0,1,24.3333-17.41978A100.44256,100.44256,0,0,1,736.743,315.61475c.70319-.09065.70886,1.01461.01026,1.10467Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M686.44718,337.79134a14.807,14.807,0,0,1,1.63241-19.1039c.50628-.49873,1.30506.26457.79811.764a13.71094,13.71094,0,0,0-1.48216,17.77371c.41512.5769-.53561,1.13983-.94836.56623Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M670.36216,363.49127a28.53932,28.53932,0,0,0,20.3938-4.08346c.59834-.38471,1.16384.56412.56622.94836a29.68517,29.68517,0,0,1-21.23023,4.20607c-.70085-.12626-.42683-1.19655.27021-1.071Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M714.44656,321.9478a8.38148,8.38148,0,0,0,6.2686,4.89443c.7021.11732.42732,1.18753-.27021,1.071a9.39213,9.39213,0,0,1-6.94675-5.39917.57084.57084,0,0,1,.19107-.7573.55506.55506,0,0,1,.75729.19107Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M762.46124,397.11454c-.44048-.06079-.881-.12157-1.32791-.1756a110.37862,110.37862,0,0,0-17.88208-.90839c-.46221.00673-.93053.02051-1.39159.0405a116.3646,116.3646,0,0,0-41.75015,9.61014,113.00482,113.00482,0,0,0-15.16291,8.0555c-6.68773,4.23438-13.602,9.35764-21.078,11.08459a19.38584,19.38584,0,0,1-2.36217.42086l-30.88864-26.74546c-.03969-.096-.0858-.18531-.12584-.28162l-1.28212-1.01147c.23872-.17556.49008-.35251.72879-.52808.138-.10241.283-.19887.421-.30128.09422-.06639.18881-.13253.27-.19782.03128-.02222.0629-.04413.08811-.05934.08122-.06529.1636-.11732.23871-.17556q2.10345-1.4895,4.23516-2.95463c.00611-.007.00611-.007.0191-.00815a166.15689,166.15689,0,0,1,34.601-18.59939c.36686-.13859.73948-.28453,1.12045-.4109a107.831,107.831,0,0,1,16.93919-4.76651,95.32878,95.32878,0,0,1,9.5528-1.33433,79.272,79.272,0,0,1,24.72335,1.7516c16.14332,3.7433,30.90977,12.60785,39.65578,26.43254C762.02688,396.40555,762.24387,396.75367,762.46124,397.11454Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M762.05235,397.44645a98.69236,98.69236,0,0,0-59.45156-12.3533A42.50006,42.50006,0,0,0,689.69,388.35387a24.3758,24.3758,0,0,0-9.78493,8.29673c-2.36313,3.30088-4.39808,6.951-7.52245,9.62a14.92533,14.92533,0,0,1-11.76132,3.26575c-5.2028-.6506-9.86156-3.13185-14.3331-5.71664-4.9648-2.86991-10.0762-5.92951-15.93241-6.34685-.70956-.05056-.5896-1.14861.11888-1.09812,10.1888.72611,17.633,8.8707,27.22462,11.46035,4.47564,1.20837,9.34256,1.07528,13.18213-1.77925,3.35754-2.49617,5.45923-6.25839,7.82305-9.62129a26.01082,26.01082,0,0,1,9.26529-8.46889,39.29037,39.29037,0,0,1,12.73777-3.74506,95.74907,95.74907,0,0,1,29.91669.7416,100.44263,100.44263,0,0,1,32.085,11.59611c.616.351-.04488,1.23688-.65689.88819Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M709.199,383.98345a14.807,14.807,0,0,1,12.80526-14.27057c.7045-.09339.88272.997.17729,1.0905a13.711,13.711,0,0,0-11.88443,13.29895c-.01588.71056-1.11391.58761-1.09812-.11888Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M680.88287,394.81911a28.53928,28.53928,0,0,0,18.74183,9.01806c.70936.05308.58963,1.15113-.11888,1.09812a29.68518,29.68518,0,0,1-19.4835-9.42375c-.48357-.52277.37961-1.21236.86055-.69243Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M741.09383,388.19084a8.38147,8.38147,0,0,0,2.05834,7.68205c.49.51638-.37378,1.20545-.86055.69243a9.39216,9.39216,0,0,1-2.29591-8.49336.57082.57082,0,0,1,.6085-.48962.55506.55506,0,0,1,.48962.6085Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M219.92162,754.74293c-1.45,5.44-5.26,9.97-9.86,13.27-.75.54-1.52,1.04-2.3,1.51-.24.14-.48.29-.73.42q-.405.24-.81.45h-21.63c-.39-.79-.77-1.59-1.15-2.38-9.27-19.48-15.78-40.5-14.67-61.91a79.25417,79.25417,0,0,1,5.17-24.25c5.94-15.47,16.78-28.86,31.69-35.6.37-.17.76-.34,1.14-.5-.12.43-.24.85-.36,1.28a110.78533,110.78533,0,0,0-3.38,17.59c-.06.46-.11.92-.15,1.39a116.05427,116.05427,0,0,0,3.72,42.69c.01.03.01995.07.03.1q1.27506,4.605,2.96,9.07c.88,2.35,1.83,4.67,2.87,6.95C216.80163,734.393,222.62157,744.593,219.92162,754.74293Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M207.04162,646.203c-.21.28-.42005.55-.63.83a98.12885,98.12885,0,0,0-11.12,18.76c-.16.33-.31.66-.44,1a97.8135,97.8135,0,0,0-7.82,29.24,1.49,1.49,0,0,0-.02.21c-.25,2.36005-.4,4.74-.46,7.12a42.48011,42.48011,0,0,0,1.43,13.24,23.7688,23.7688,0,0,0,5.46,9.42c.25.27.5.54.77.8.2.21.42.42.63.62,2.02,1.93,4.23,3.72,6.13,5.79a21.43163,21.43163,0,0,1,2.35,3,14.90407,14.90407,0,0,1,1.6,12.1c-1.36,5.06-4.47,9.33-7.65,13.4-1.59,2.04-3.23,4.1-4.65,6.28-.51995.78-1,1.57-1.43994,2.38h-1.26c.42-.81.88-1.6,1.38-2.38,3.65-5.75,8.84-10.69,11.53-17.02,1.82-4.26995,2.37-9.11.07-13.3a17.68156,17.68156,0,0,0-2.43-3.38c-1.83-2.07-4.02-3.84-6.01-5.71-.5-.47-.99-.95-1.46-1.45a24.96377,24.96377,0,0,1-5.64-8.9,39.23028,39.23028,0,0,1-1.94-13.13c0-2.84.15-5.7.43-8.54.03-.36.07-.73.11-1.1a100.76663,100.76663,0,0,1,19.67-49.23c.2-.28.41-.55.62-.82C206.68163,644.87294,207.47161,645.653,207.04162,646.203Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M186.36526,696.67763a14.807,14.807,0,0,1-12.3542-14.66278.55275.55275,0,0,1,1.10455-.02415,13.711,13.711,0,0,0,11.51986,13.616c.70147.11439.42725,1.18471-.27021,1.071Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M193.164,726.22406a28.5393,28.5393,0,0,0,11.53315-17.308c.15106-.69512,1.22186-.42407,1.071.27021a29.68514,29.68514,0,0,1-12.0379,17.98619c-.58485.40629-1.1479-.54428-.56622-.94836Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M194.96075,665.676a8.38149,8.38149,0,0,0,7.89345-.97168c.57941-.41351,1.14186.53754.56622.94836a9.39215,9.39215,0,0,1-8.72989,1.09429.57082.57082,0,0,1-.40038-.67059.55507.55507,0,0,1,.6706-.40038Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M282.06158,684.87294c-.35.27-.71.54-1.06.82a110.362,110.362,0,0,0-13.29,12c-.32.33-.64.67-.95,1.01l-.01.01a116.347,116.347,0,0,0-22.66,36.14l-.03.09c-.01.03-.02.05-.03.08a114.44321,114.44321,0,0,0-5.03,16.42c-1.22,5.46-2.22,11.31-4.13,16.57-.29.81-.61,1.61-.95,2.38h-44.46c.15-.79.31-1.59.47-2.38a160.30168,160.30168,0,0,1,10.54-33.7c.16-.36.32-.72.5-1.08a108.30478,108.30478,0,0,1,8.61-15.35.0098.0098,0,0,1,.01-.01,94.95585,94.95585,0,0,1,5.8-7.69,79.11871,79.11871,0,0,1,18.72-16.24c.04-.03.09-.05.13-.08,14.04-8.71,30.68-12.86,46.59-9.27h.01C281.25158,684.68294,281.6516,684.773,282.06158,684.87294Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M282.01159,685.403c-.34.09-.68.19-1.01.29a98.5888,98.5888,0,0,0-20.17,8.27c-.32.17-.64.35-.96.53a98.25544,98.25544,0,0,0-23.79,18.59.035.035,0,0,0-.01.02c-.08.08-.17.17-.24.25-1.6,1.72-3.14,3.51-4.6,5.35a42.769,42.769,0,0,0-6.82,11.43,23.67365,23.67365,0,0,0-1.31,10.81c.03.37.08.73.13,1.1.04.29.08.58.13.88.66,4.01,1.8,8.03,1.48,12.12a14.90913,14.90913,0,0,1-6.01,10.63,23.794,23.794,0,0,1-3.68,2.34,36.85232,36.85232,0,0,1-5.77,2.38h-3.93c.53-.15,1.05-.3,1.58-.45a48.21182,48.21182,0,0,0,5.53-1.93,26.912,26.912,0,0,0,3-1.48c4.02-2.31,7.37005-5.85,8.07-10.58.61-4.14-.57-8.28-1.27-12.33-.12-.7-.23-1.39-.29-2.08a24.43856,24.43856,0,0,1,.85-10.46,39.0623,39.0623,0,0,1,6.36-11.66,83.355,83.355,0,0,1,5.48-6.55q.36-.40494.75-.81a100.901,100.901,0,0,1,24.21-18.73h.01a99.28782,99.28782,0,0,1,21.1-8.74h.01c.33-.1.67-.2,1-.29C282.53161,684.12294,282.69158,685.213,282.01159,685.403Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M235.116,713.25243a14.807,14.807,0,0,1-1.03613-19.1455c.43212-.5642,1.32915.08079.89646.64574A13.711,13.711,0,0,0,235.97653,712.56c.49121.51367-.37215,1.20316-.86055.69243Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M222.75543,740.93692a28.53931,28.53931,0,0,0,19.62921-6.87574c.53912-.46406,1.2309.397.69242.86054a29.68514,29.68514,0,0,1-20.44051,7.11332c-.71159-.02772-.58885-1.12569.11888-1.09812Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M260.64411,693.67444a8.38149,8.38149,0,0,0,6.8875,3.97657c.71159.01869.58807,1.11668-.11888,1.09812a9.39215,9.39215,0,0,1-7.62917-4.38226.57083.57083,0,0,1,.08406-.77649.55507.55507,0,0,1,.77649.08406Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M625.03076,300.73673a11.59945,11.59945,0,0,1-17.7667.83759l-37.80039,16.44009,3.682-21.10161,35.3314-12.37668a11.66235,11.66235,0,0,1,16.55372,16.20061Z" transform="translate(-168.64838 -127.60704)" fill="#ffb8b8"/><path d="M599.80571,307.32525l-87.7976,39.10831-.18835-.06738-100.067-35.65889a32.95966,32.95966,0,0,1-14.78168-42.75569h0a32.92423,32.92423,0,0,1,46.9872-14.63652l74.4685,44.85908,72.21121-9.35878Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M1031.35162,771.203a1.1865,1.1865,0,0,1-1.19,1.19h-860.29a1.19,1.19,0,0,1,0-2.38h860.29A1.1865,1.1865,0,0,1,1031.35162,771.203Z" transform="translate(-168.64838 -127.60704)" fill="#ccc"/><path d="M481.99193,424.40352l-88.50585-14.15674a16.89334,16.89334,0,0,1-9.95557-23.646l4.01367-8.02832-1.55908-84.34668A62.48156,62.48156,0,0,1,416.32152,239.572l8.63086-5.16064,4.36182-11.07666,40.22022.981.11718,14.52734,14.40381,22.96826-.00049.09522-.90381,125.01367-3.96972,12.90137,6.00244,15.00586Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><circle cx="284.4591" cy="45.40997" r="36.54413" fill="#ffb8b8"/><path d="M415.05385,180.98352c-1.09-4.59187-.58956-11.05349.02641-15.677,1.61485-12.12129,8.3464-23.64474,18.57336-30.47048a13.37957,13.37957,0,0,1,6.66453-2.64845c2.41939-.101,5.04189,1.19418,5.78465,3.499a11.99254,11.99254,0,0,1,6.76552-6.709,21.1355,21.1355,0,0,1,9.63075-1.29746,35.19728,35.19728,0,0,1,29.36306,20.98947c.97609,2.3188,3.70246-6.24621,4.93916-4.05528a9.7407,9.7407,0,0,0,5.52388,4.85342c2.4233.67619,3.40756,10.66034,4.3612,8.33222a11.0984,11.0984,0,0,1-10.61055,15.47525c-2.46642-.09228-4.82489-.99947-7.262-1.39-8.71512-1.39642-17.96,4.92316-19.82312,13.55058a23.98689,23.98689,0,0,0-3.15565-7.021,8.1187,8.1187,0,0,0-6.51321-3.57866c-2.47957.09278-4.6591,1.7139-6.26793,3.60295s-2.81713,4.093-4.43782,5.97186c-4.7555,5.513-11.18745,18.3697-17.96453,17.432C425.30335,201.103,416.54206,187.25309,415.05385,180.98352Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M674.01238,342.14754a7.1328,7.1328,0,0,0-4.80706-7.85363l-98.41317-32.77709a7.13219,7.13219,0,0,0-2.933-.3368l-24.66687,2.33267-14.15377,1.34255-26.11867,2.46833a7.15519,7.15519,0,0,0-6.38357,5.98973l-13.26135,82.8376a7.18646,7.18646,0,0,0,4.48439,7.79592l99.4404,38.38442a6.94669,6.94669,0,0,0,1.44636.38836,7.13621,7.13621,0,0,0,2.17571.01648l64.25546-9.52349a7.12057,7.12057,0,0,0,6.023-5.99919Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><path d="M490.01349,398.1102l99.44009,38.38234a.89711.89711,0,0,0,.457.05366l64.247-9.52224a.88347.88347,0,0,0,.7549-.75161l12.91979-85.06677a.90469.90469,0,0,0-.59937-.98151l-.66169-.22392-97.75762-32.54588a.67787.67787,0,0,0-.13742-.03318.88732.88732,0,0,0-.23-.01192l-60.16426,5.6932-4.77428.44794a.90314.90314,0,0,0-.7947.74781l-13.259,82.83439A.89735.89735,0,0,0,490.01349,398.1102Z" transform="translate(-168.64838 -127.60704)" fill="#6b4ce8"/><path d="M508.28194,313.10237l60.16426-5.6932a.88732.88732,0,0,1,.23.01192.67787.67787,0,0,1,.13742.03318l97.75762,32.54588-25.78658,2.72965-9.65046,1.01669-27.46045,2.90123a1.939,1.939,0,0,1-.24081-.0029c-.04881-.01472-.09762-.02944-.15639-.04511Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M487.75761,403.95209l99.44009,38.38233a6.72242,6.72242,0,0,0,1.4505.37968,7.22358,7.22358,0,0,0,2.17727.02722l64.247-9.52224a7.13521,7.13521,0,0,0,6.02839-6.00387l12.90982-85.06772a7.19014,7.19014,0,0,0-.4184-3.71669c-.06533-.15688-.13072-.31384-.207-.46172a6.99031,6.99031,0,0,0-2.26369-2.69758,7.13789,7.13789,0,0,0-1.91579-.97662l-.11659-.04131-98.29175-32.73751a8.95539,8.95539,0,0,0-1.22721-.29807,7.08573,7.08573,0,0,0-1.71463-.03323l-24.66295,2.32468-14.15253,1.35L502.917,307.3259a7.09173,7.09173,0,0,0-3.01853.99744,1.32948,1.32948,0,0,0-.20245.12125,1.1922,1.1922,0,0,0-.12992.09813,7.14818,7.14818,0,0,0-3.02682,4.76367l-13.2699,82.84346A7.19418,7.19418,0,0,0,487.75761,403.95209Zm10.54219-90.35694a5.29965,5.29965,0,0,1,1.26984-2.6713,4.65147,4.65147,0,0,1,.67571-.65875,5.31719,5.31719,0,0,1,2.32365-1.08389,4.059,4.059,0,0,1,.50915-.07189l43.98466-4.15521,20.96479-1.995c.14217-.01658.27254-.01418.40386-.02168a5.00673,5.00673,0,0,1,.94761.07043,4.14489,4.14489,0,0,1,.84467.20125l98.4084,32.77882c.07775.02754.14554.05407.22323.0816a5.218,5.218,0,0,1,2.27305,1.6537,5.25912,5.25912,0,0,1,1.12074,4.14541l-12.92068,85.07674a5.34916,5.34916,0,0,1-4.5086,4.50155l-64.257,9.52134a5.41346,5.41346,0,0,1-2.72281-.31038l-99.441-38.37237a5.40237,5.40237,0,0,1-3.35921-5.846Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M499.35216,308.99439a.87724.87724,0,0,1,.268-.38623,1.05132,1.05132,0,0,1,.129-.08817c.04169-.01607.08434-.04216.12611-.05828a.87349.87349,0,0,1,.62383-.01066l2.06994.73016,101.1157,35.66943,23.66513-2.5004,13.24288-1.39675,28.02932-2.96742,2.50639-.26279.48732-.05387a.9043.9043,0,0,1,.95216.65352.73938.73938,0,0,1,.02649.14313.893.893,0,0,1-.55014.92188.98843.98843,0,0,1-.24752.06673l-3.40944.35738-27.60268,2.91775-9.65046,1.01669-27.46045,2.90123a1.939,1.939,0,0,1-.24081-.0029c-.04881-.01472-.09762-.02944-.15639-.04511L500.24535,310.2651l-.3498-.1238a.67025.67025,0,0,1-.21942-.12146A.91016.91016,0,0,1,499.35216,308.99439Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M588.91905,442.97456a.89376.89376,0,0,1-.74251-1.01574l14.51687-96.33414a.894.894,0,0,1,1.017-.75056l.008.00129a.89377.89377,0,0,1,.74252,1.01574l-14.51687,96.33414a.894.894,0,0,1-1.017.75055Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M625.716,436.86342l-9.6548,1.01888,11.29337-95.5347s12.89458-2.33464,13.23951-1.39846C640.80631,341.50808,625.80805,436.25066,625.716,436.86342Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><polygon points="331.25 182.533 330.99 226.1 408.116 255.488 435.813 218.284 331.25 182.533" fill="#3f3d56"/><path d="M671.13144,337.72465a5.30105,5.30105,0,0,0-2.49688-1.73654l-98.40594-32.7777a5.10582,5.10582,0,0,0-.848-.20665,5.00894,5.00894,0,0,0-.95065-.07115l.15966-.99731.98511-.71323,23.36822-16.9188,78.04053,23.91705.13549,27.05154Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M503.829,380.07963a1.51326,1.51326,0,0,1,.326.06843l30.19365,9.91686a1.50014,1.50014,0,0,1-.93555,2.85069l-30.19364-9.91685a1.50039,1.50039,0,0,1,.60952-2.91913Z" transform="translate(-168.64838 -127.60704)" fill="#fff"/><circle cx="457.00322" cy="423.23593" r="12" fill="#f2f2f2"/><circle cx="151.00322" cy="467.23593" r="12" fill="#f2f2f2"/><circle cx="401.00322" cy="70.23593" r="12" fill="#f2f2f2"/><path d="M589.34024,397.72852A11.59947,11.59947,0,0,1,573.433,389.7714L532.421,385.62792l13.53022-16.60628,36.87128,6.48065a11.66236,11.66236,0,0,1,6.5177,22.22623Z" transform="translate(-168.64838 -127.60704)" fill="#ffb8b8"/><path d="M564.115,391.14082l-95.70849-8.81836-.13135-.15088L398.42455,302.135a32.95967,32.95967,0,0,1,8.01319-44.52344h0a32.92425,32.92425,0,0,1,48.14355,10.209l43.02246,75.54443,67.56543,27.147Z" transform="translate(-168.64838 -127.60704)" fill="#e6e6e6"/><path d="M804.33859,237.22376c-2.37688-17.43387-5.35788-36.15172-17.65411-48.7369a41.34992,41.34992,0,0,0-59.74384.61837c-8.95079,9.54876-12.90365,22.95672-13.2654,36.03983s2.55205,26.02081,5.78442,38.70347a119.28958,119.28958,0,0,0,49.78577-9.79937c3.92617-1.70407,7.789-3.63056,11.93689-4.68634,4.14784-1.05571,7.10454,1.60088,10.96292,3.45335l2.118-4.05545c1.73377,3.22659,7.10244,2.27017,9.04978-.83224C805.26007,244.82608,804.83352,240.853,804.33859,237.22376Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/><path d="M736.532,334.53244l-69.876,1.49441a11.05455,11.05455,0,1,0-4.93974,15.57383c9.26761.52674,81.77191,10.81733,86.0974,4.18549,4.39027-6.73106,27.82423-30.48612,27.82423-30.48612l-18.01271-25.64378Z" transform="translate(-168.64838 -127.60704)" fill="#9e616a"/><circle cx="584.91096" cy="94.03525" r="32.83012" fill="#9e616a"/><path d="M599.36147,299.184" transform="translate(-168.64838 -127.60704)" fill="#6b4ce8"/><path d="M806.14195,284.81075c-3.86888-7.69981-5.74873-17.212-13.99671-19.70823-5.56965-1.68563-28.09691.84048-33.17312,3.6859-8.44356,4.73313-.79189,13.60234-5.77332,21.90214-5.41517,9.02271-20.132,27.12978-25.5472,36.15241-3.72279,6.20279,8.8171,24.40947,6.80408,31.358-2.01273,6.94848-2.10962,14.74736,1.31952,21.11722,3.06888,5.70141-1.37137,10.745,1.71521,16.437,3.20957,5.91962,7.14849,28.05274,4.16119,34.08785l-2,6c19.84682,1.16609,36.53459-22.54427,56.25813-25.04188,4.89894-.62032,9.98565-1.43073,14.02251-4.27435,5.94639-4.18864,8.29717-11.78923,9.76638-18.91282A159.32576,159.32576,0,0,0,806.14195,284.81075Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M835.89793,366.11245c-2.76443-7.54563-7.769-40.5366-7.769-40.5366l-31.32417-.91848,15.31443,37.772-41.79036,58.50283s.07739.12853.21808.35778a11.052,11.052,0,1,0,9.26964,11.74483.76305.76305,0,0,0,.95807-.16445C785.42465,427.035,838.66236,373.65815,835.89793,366.11245Z" transform="translate(-168.64838 -127.60704)" fill="#9e616a"/><path d="M839.0826,345.27741c-2.87511-12.13478-5.77152-24.33549-10.61887-35.82566s-11.78661-22.34286-21.54669-30.10543c-3.12048-2.48179-6.609-4.67232-10.52078-5.44389-3.91147-.77165-8.31967.09193-11.0667,2.98137-4.39621,4.62357-3.07339,12.0451-1.4611,18.21781Q791,322.40224,798.13123,349.70286q20.59418-2.18287,41.188-4.36591Z" transform="translate(-168.64838 -127.60704)" fill="#3f3d56"/><path d="M793.7871,226.19592c-1.20908-7.942-2.47188-15.95043-5.31228-23.42857-2.8404-7.47821-7.41882-14.48249-13.98647-18.71882-10.39879-6.70709-23.862-5.41352-35.52074-1.55544-9.01622,2.9837-17.81761,7.51864-24.17574,14.8093-6.35848,7.29074-9.92957,17.69379-7.56439,27.22665q18.65464-4.40738,37.30893-8.81483l-1.36137.962a30.03765,30.03765,0,0,1,16.03083,20.8927,31.12209,31.12209,0,0,1-6.56554,25.84773q12.72244-4.51323,25.44489-9.0263c5.23526-1.85713,10.83833-3.997,13.94267-8.76047C795.62723,240.107,794.79091,232.78685,793.7871,226.19592Z" transform="translate(-168.64838 -127.60704)" fill="#2f2e41"/></svg>
	`,
	privilege: `
<svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <title>Billing Settings</title>
  <path d="M22 15.8235H14.65M4.15 15.8235H1M22 4.17647H18.85M8.35 4.17647H1M6.25 12.6471H12.55C13.705 12.6471 14.65 13.1765 14.65 14.7647V16.8824C14.65 18.4706 13.705 19 12.55 19H6.25C5.095 19 4.15 18.4706 4.15 16.8824V14.7647C4.15 13.1765 5.095 12.6471 6.25 12.6471ZM10.45 1H16.75C17.905 1 18.85 1.52941 18.85 3.11765V5.23529C18.85 6.82353 17.905 7.35294 16.75 7.35294H10.45C9.295 7.35294 8.35 6.82353 8.35 5.23529V3.11765C8.35 1.52941 9.295 1 10.45 1Z"
        stroke="currentColor" stroke-width="1.3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  users: `
<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <title>Community Group</title>
  <path d="M14.4943 12.1946C15.7295 12.4016 17.0909 12.1856 18.0466 11.5467C19.3178 10.7008 19.3178 9.31494 18.0466 8.46905C17.0819 7.83013 15.7025 7.61415 14.4673 7.83012M5.50566 12.1946C4.27049 12.4016 2.9091 12.1856 1.95342 11.5467C0.682192 10.7008 0.682192 9.31494 1.95342 8.46905C2.91812 7.83013 4.29754 7.61415 5.53271 7.83012M15.4231 5.64342C15.369 5.63443 15.3059 5.63443 15.2518 5.64342C14.0076 5.59843 13.0159 4.58155 13.0159 3.32171C13.0159 2.03487 14.0527 1 15.3419 1C16.6312 1 17.668 2.04387 17.668 3.32171C17.659 4.58155 16.6673 5.59843 15.4231 5.64342ZM4.57701 5.64342C4.63111 5.63443 4.69422 5.63443 4.74831 5.64342C5.9925 5.59843 6.98424 4.58155 6.98424 3.32171C6.98424 2.03487 5.94742 1 4.65816 1C3.36889 1 2.33207 2.04387 2.33207 3.32171C2.34109 4.58155 3.33283 5.59843 4.57701 5.64342ZM10.0157 12.3656C9.9616 12.3566 9.89849 12.3566 9.84439 12.3656C8.60021 12.3206 7.60847 11.3037 7.60847 10.0439C7.60847 8.75702 8.64529 7.72215 9.93455 7.72215C11.2238 7.72215 12.2606 8.76602 12.2606 10.0439C12.2516 11.3037 11.2599 12.3296 10.0157 12.3656ZM7.3921 15.2002C6.12087 16.0461 6.12087 17.4319 7.3921 18.2778C8.83463 19.2407 11.1968 19.2407 12.6393 18.2778C13.9105 17.4319 13.9105 16.0461 12.6393 15.2002C11.2058 14.2463 8.83463 14.2463 7.3921 15.2002Z"
        stroke="currentColor" stroke-width="1.3" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

`,
  threeDots: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
  <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0"/>
</svg>`,
 role: `
		<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <title>User Profile</title>
  <path d="M15.5261 18.658C14.7341 18.892 13.7981 19 12.7001 19H7.3001C6.2021 19 5.2661 18.892 4.4741 18.658M15.5261 18.658C15.3281 16.318 12.9251 14.4729 10.0001 14.4729C7.0751 14.4729 4.6721 16.318 4.4741 18.658M15.5261 18.658C17.9741 17.965 19 16.1022 19 12.7002V7.3001C19 2.80003 17.2 1 12.7 1H7.3C2.8 1 1 2.80003 1 7.3001V12.7002C1 16.1022 2.0261 17.965 4.4741 18.658M13.222 8.72228C13.222 10.5043 11.782 11.9533 10 11.9533C8.21802 11.9533 6.77802 10.5043 6.77802 8.72228C6.77802 6.94025 8.21802 5.50021 10 5.50021C11.782 5.50021 13.222 6.94025 13.222 8.72228Z"
        stroke="currentColor" stroke-width="1.3" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
	`,
};
