import ApiService from "@/service/ApiService";
import type { Provider } from "../store/providersStore";

const api = new ApiService()

const path = '/claimconnect/provider'

export function getProviders(query = {}) {
	return api.addAuthenticationHeader().get<Provider[]>(`${path}/list/fetchFromProvider`, {
		params: query
	})
}

export function createProvider(data: any) {
	return api.addAuthenticationHeader().post(`${path}`, data)
}

