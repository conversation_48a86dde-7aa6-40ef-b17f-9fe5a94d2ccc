@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-Thin.woff') format('woff');
	font-weight: 100; /* Thin */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-UltraLight.woff') format('woff');
	font-weight: 200; /* Ultra Light */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-Light.woff') format('woff');
	font-weight: 300; /* Light */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-Regular.woff') format('woff');
	font-weight: 400; /* Regular */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-Medium.woff') format('woff');
	font-weight: 500; /* Medium */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-SemiBold.woff') format('woff');
	font-weight: 600; /* Semibold */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-Bold.woff') format('woff');
	font-weight: 700; /* Bold */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-Black.woff') format('woff');
	font-weight: 800; /* Black */
	font-style: normal;
}

@font-face {
	font-family: 'Geist';
	src: url('/src/assets/fonts/geist/Geist-UltraBlack.woff') format('woff');
	font-weight: 900; /* Extra Black */
	font-style: normal;
}