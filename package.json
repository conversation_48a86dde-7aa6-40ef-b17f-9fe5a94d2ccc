{"name": "claim_connect_redesign", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force"}, "dependencies": {"@customizer/modal-x": "^0.0.88", "@vueuse/components": "^13.6.0", "autoprefixer": "^10.4.20", "axios": "^1.7.5", "chart.js": "^4.4.9", "pinia": "^2.1.7", "postcss": "^8.4.45", "tailwindcss": "^3.4.11", "vue": "^3.4.29", "vue-router": "^4.3.3"}, "devDependencies": {"@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.5", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/tsconfig": "^0.5.1", "npm-run-all2": "^6.2.0", "typescript": "~5.4.0", "vite": "^5.3.1", "vite-plugin-vue-devtools": "^7.3.1", "vue-tsc": "^2.0.21"}}