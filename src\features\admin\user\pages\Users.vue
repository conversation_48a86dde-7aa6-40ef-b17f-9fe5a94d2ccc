<script setup>
import { ref } from "vue";
import Table from "@/components/Table.vue";
import TableRowSkeleton from "@/components/TableRowSkeleton.vue";
import DefaultPage from "@/components/DefaultPage.vue";
import icons from "@/utils/icons";
import UserStatusRow from "../components/statusrow.vue";
import UsersDataProvider from "../components/UsersDataProvider.vue";
import { openModal } from "@customizer/modal-x";
</script>

<template>
  <DefaultPage v-model="search" placeholder="Search Users">
    <template #add-action>
      <button
        class="flex justify-center items-center gap-2 rounded-md px-6 py-4 bg-primary text-white"
        @click.prevent="openModal('AddUser')"
      >
        <i v-html="icons.plus_circle"></i>
        Add User
      </button>
    </template>

    <template #default="{ search }">
      <UsersDataProvider :search="search" v-slot="{ users, pending, refresh }">
        <Table
          :pending="pending"
          :headers="{
            head: [
              'Fullname',
              'Email',
              'Mobile Phone',
              'User Type',
              'Role Name',
              'Gender',
              'Status',
              'Actions',
            ],
            row: [
              'fullname',
              'email',
              'mobilePhone',
              'userType',
              'roleName',
              'gender',
              'status',
            ],
          }"
          :rows="users"
          :rowCom="UserStatusRow"
          :Fallback="TableRowSkeleton"
        />
      </UsersDataProvider>
    </template>
  </DefaultPage>
</template>
